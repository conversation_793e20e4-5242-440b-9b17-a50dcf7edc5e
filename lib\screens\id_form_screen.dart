import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'cryptograph_display_screen.dart';
import '../services/database_service.dart';
import '../models/person.dart';

class IdFormScreen extends StatefulWidget {
  final String idType;
  
  const IdFormScreen({super.key, required this.idType});

  @override
  State<IdFormScreen> createState() => _IdFormScreenState();
}

class _IdFormScreenState extends State<IdFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _idNumberController = TextEditingController();
  final _departmentController = TextEditingController();
  final _positionController = TextEditingController();
  final _companyController = TextEditingController();
  final _addressController = TextEditingController();
  final _emergencyContactController = TextEditingController();
  
  File? _imageFile;
  Uint8List? _webImage;
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _idNumberController.dispose();
    _departmentController.dispose();
    _positionController.dispose();
    _companyController.dispose();
    _addressController.dispose();
    _emergencyContactController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        if (kIsWeb) {
          final bytes = await pickedFile.readAsBytes();
          setState(() {
            _webImage = bytes;
          });
        } else {
          setState(() {
            _imageFile = File(pickedFile.path);
          });
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking image: $e')),
      );
    }
  }

  Future<void> _generateCryptograph() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (_imageFile == null && _webImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a photo')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Check if person already exists
      final existingPerson = await DatabaseService.instance.getPersonByIdNumber(_idNumberController.text);
      
      if (existingPerson != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Person with ID ${_idNumberController.text} already exists!'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Create person object
      final person = Person(
        name: _nameController.text,
        email: _emailController.text,
        phone: _phoneController.text,
        idNumber: _idNumberController.text,
        idType: widget.idType,
        department: _departmentController.text,
        position: _positionController.text,
        company: _companyController.text,
        address: _addressController.text,
        emergencyContact: _emergencyContactController.text,
        photoPath: '', // Will be set after saving
        createdAt: DateTime.now(),
      );

      // Save to database
      await DatabaseService.instance.insertPerson(person);

      // Navigate to cryptograph display
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CryptographDisplayScreen(
              person: person,
              imageFile: _imageFile,
              webImage: _webImage,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  List<Widget> _buildDynamicFields() {
    List<Widget> fields = [
      // Common fields for all types
      _buildTextField(_nameController, 'Full Name', Icons.person, true),
      const SizedBox(height: 16),
      _buildTextField(_emailController, 'Email', Icons.email, true),
      const SizedBox(height: 16),
      _buildTextField(_phoneController, 'Phone Number', Icons.phone, true),
      const SizedBox(height: 16),
      _buildTextField(_idNumberController, 'ID Number', Icons.badge, true),
      const SizedBox(height: 16),
    ];

    // Add specific fields based on ID type
    switch (widget.idType) {
      case 'School':
        fields.addAll([
          _buildTextField(_departmentController, 'Department/Faculty', Icons.school, true),
          const SizedBox(height: 16),
          _buildTextField(_positionController, 'Student/Staff Position', Icons.work, true),
          const SizedBox(height: 16),
          _buildTextField(_emergencyContactController, 'Emergency Contact', Icons.emergency, true),
        ]);
        break;
      case 'Professional':
        fields.addAll([
          _buildTextField(_companyController, 'Company Name', Icons.business, true),
          const SizedBox(height: 16),
          _buildTextField(_departmentController, 'Department', Icons.corporate_fare, true),
          const SizedBox(height: 16),
          _buildTextField(_positionController, 'Job Title', Icons.work, true),
          const SizedBox(height: 16),
          _buildTextField(_addressController, 'Office Address', Icons.location_on, false),
        ]);
        break;
      case 'Business':
        fields.addAll([
          _buildTextField(_companyController, 'Business Name', Icons.business, true),
          const SizedBox(height: 16),
          _buildTextField(_positionController, 'Title/Role', Icons.work, true),
          const SizedBox(height: 16),
          _buildTextField(_addressController, 'Business Address', Icons.location_on, true),
          const SizedBox(height: 16),
          _buildTextField(_departmentController, 'Industry/Sector', Icons.category, false),
        ]);
        break;
    }

    return fields;
  }

  Widget _buildTextField(TextEditingController controller, String label, IconData icon, bool required) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label + (required ? ' *' : ''),
          prefixIcon: Icon(icon),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(20),
        ),
        validator: required ? (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter $label';
          }
          return null;
        } : null,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          '${widget.idType} ID Form',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[800],
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue[800]!,
              Colors.blue[600]!,
              Colors.grey[100]!,
            ],
          ),
        ),
        child: SafeArea(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        // Photo Upload Section
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              const Text(
                                'Person Photo',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              GestureDetector(
                                onTap: _pickImage,
                                child: Container(
                                  width: 150,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: Colors.grey[400]!,
                                      width: 2,
                                      style: BorderStyle.solid,
                                    ),
                                  ),
                                  child: _imageFile != null || _webImage != null
                                      ? ClipRRect(
                                          borderRadius: BorderRadius.circular(13),
                                          child: kIsWeb
                                              ? Image.memory(
                                                  _webImage!,
                                                  fit: BoxFit.cover,
                                                )
                                              : Image.file(
                                                  _imageFile!,
                                                  fit: BoxFit.cover,
                                                ),
                                        )
                                      : Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.add_a_photo,
                                              size: 40,
                                              color: Colors.grey[600],
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              'Tap to add photo',
                                              style: TextStyle(
                                                color: Colors.grey[600],
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        
                        // Dynamic Form Fields
                        ..._buildDynamicFields(),
                      ],
                    ),
                  ),
                ),
                
                // Generate Button
                Container(
                  padding: const EdgeInsets.all(24),
                  child: SizedBox(
                    width: double.infinity,
                    height: 60,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _generateCryptograph,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.blue[800],
                        elevation: 8,
                        shadowColor: Colors.black.withOpacity(0.3),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                      ),
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.security, size: 24),
                                const SizedBox(width: 12),
                                const Text(
                                  'Generate Cryptograph',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
