@echo off
echo ========================================
echo    PHONE DEBUG TROUBLESHOOTING
echo ========================================
echo.

echo [1] Checking ADB connection...
C:\Users\<USER>\AppData\Local\Android\sdk\platform-tools\adb.exe devices
echo.

echo [2] Restarting ADB server...
C:\Users\<USER>\AppData\Local\Android\sdk\platform-tools\adb.exe kill-server
C:\Users\<USER>\AppData\Local\Android\sdk\platform-tools\adb.exe start-server
echo.

echo [3] Checking devices again...
C:\Users\<USER>\AppData\Local\Android\sdk\platform-tools\adb.exe devices
echo.

echo [4] Checking Flutter devices...
C:\flutter\bin\flutter.bat devices
echo.

echo ========================================
echo    TROUBLESHOOTING STEPS
echo ========================================
echo.
echo If no devices are shown:
echo 1. Check USB cable (must be data cable, not just charging)
echo 2. Enable Developer Options on phone:
echo    - Settings ^> About Phone ^> Tap Build Number 7 times
echo 3. Enable USB Debugging:
echo    - Settings ^> Developer Options ^> USB Debugging ON
echo 4. Authorize computer when dialog appears on phone
echo 5. Try different USB port
echo 6. Try "File Transfer" mode instead of "Charging only"
echo.
pause
