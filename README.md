# CryptoID - Cryptographic Identification Card Generator

A Flutter mobile application for generating and scanning cryptographic identification cards with advanced security features.

## Features

### 🆔 Card Generation
- **Professional ID Cards** - Work badges with employee information
- **School ID Cards** - Student identification with academic details  
- **Business Cards** - Professional business card format

### 🔐 Security Features
- **AES-256 Encryption** - All card data is encrypted using industry-standard encryption
- **Digital Signatures** - Each card has a unique cryptographic signature
- **QR Code Integration** - Encrypted QR codes for easy verification
- **Tamper Detection** - Any modification to card data is detected during verification
- **Expiry Validation** - Cards automatically become invalid after expiry date

### 📱 Core Functionality
- Generate secure identification cards with personal and organizational information
- Scan QR codes to verify card authenticity and view holder information
- Store cards locally with SQLite database
- Export card images for physical printing
- Share card information securely

## Quick Setup

### Option 1: Automated Setup (Recommended)
1. Double-click `setup_flutter.bat` to automatically install Flutter and dependencies
2. Follow the on-screen instructions
3. Connect an Android device or start an emulator
4. Run the app with: `flutter\bin\flutter run`

### Option 2: Manual Setup

#### Prerequisites
- Windows 10/11
- Android Studio (for Android development)
- Git (optional)

#### Installation Steps

1. **Install Flutter SDK**
   ```bash
   # Download Flutter SDK from https://flutter.dev/docs/get-started/install/windows
   # Extract to C:\flutter (or your preferred location)
   # Add C:\flutter\bin to your PATH environment variable
   ```

2. **Verify Installation**
   ```bash
   flutter doctor
   ```

3. **Install Dependencies**
   ```bash
   flutter pub get
   ```

4. **Run the App**
   ```bash
   # Connect Android device or start emulator
   flutter run
   ```

## App Architecture

### Core Components

- **Models** (`lib/models/`)
  - `Person` - Personal information data model
  - `IdentificationCard` - Card data structure with crypto fields

- **Services** (`lib/services/`)
  - `CryptoService` - Handles encryption, signatures, and QR code generation
  - `DatabaseService` - SQLite database operations for local storage

- **Providers** (`lib/providers/`)
  - `CardProvider` - State management for card operations

- **Screens** (`lib/screens/`)
  - `HomeScreen` - Main dashboard with statistics and navigation
  - `CardGenerationScreen` - Form for creating new cards
  - `QRScannerScreen` - Camera-based QR code scanning
  - `CardPreviewScreen` - Display generated cards with QR codes
  - `CardListScreen` - View all created cards

### Security Implementation

1. **Data Encryption**
   - Uses AES-256 encryption for QR code data
   - Secure random IV generation for each card
   - Base64 encoding for safe data transmission

2. **Digital Signatures**
   - SHA-256 hash of critical card data
   - Prevents tampering and ensures data integrity
   - Signature verification during card scanning

3. **QR Code Security**
   - Encrypted payload with essential card information
   - Includes expiry validation and signature verification
   - Compact format optimized for QR code constraints

## Usage Guide

### Creating a Card

1. **Select Card Type**
   - Choose from Professional, School, or Business card types
   - Each type has customized fields and layouts

2. **Enter Personal Information**
   - Full name, email, phone, address, date of birth
   - All fields are validated for completeness

3. **Add Organization Details**
   - Company/school/business name
   - Position/title/grade
   - Department (optional)
   - Employee/Student ID (optional)
   - Expiry date

4. **Generate Card**
   - App creates encrypted QR code and digital signature
   - Card is saved to local database
   - Preview shows final card design with QR code

### Scanning a Card

1. **Open QR Scanner**
   - Camera permission required
   - Point camera at QR code on card

2. **Verification Process**
   - App decrypts QR code data
   - Verifies digital signature
   - Checks expiry date
   - Displays verification result

3. **View Results**
   - Valid cards show green checkmark with holder information
   - Invalid/expired cards show red warning
   - All scan attempts are logged for audit purposes

## Technical Specifications

### Dependencies
- **flutter**: SDK framework
- **qr_flutter**: QR code generation
- **mobile_scanner**: QR code scanning
- **encrypt**: AES encryption
- **crypto**: SHA hashing
- **sqflite**: Local database
- **provider**: State management
- **share_plus**: Card sharing functionality

### Database Schema
- **persons**: Personal information storage
- **identification_cards**: Card data with crypto signatures
- **scan_history**: Audit log of verification attempts

### Supported Platforms
- Android 5.0+ (API level 21+)
- iOS support can be added with minimal configuration changes

## Security Considerations

### Data Protection
- All sensitive data is encrypted before storage
- Private keys are generated using secure random methods
- No personal data is transmitted over networks

### Privacy
- All data remains on device
- No cloud storage or external API calls
- User has full control over their card data

### Audit Trail
- All card generation events are logged
- Scan verification attempts are recorded
- Timestamps and status codes for compliance

## Business Applications

### B2B Contracts
- Employee verification for contractors
- Secure access control systems
- Professional credential validation

### B2G Contracts
- Government employee identification
- Secure facility access
- Compliance with security standards

### Educational Institutions
- Student identification systems
- Campus access control
- Academic credential verification

### Corporate Use
- Employee badge systems
- Visitor management
- Professional networking events

## Future Enhancements

- [ ] Biometric integration (fingerprint/face recognition)
- [ ] NFC support for contactless verification
- [ ] Cloud backup with end-to-end encryption
- [ ] Multi-language support
- [ ] Custom card templates and branding
- [ ] Integration with existing HR/student information systems
- [ ] Batch card generation for organizations
- [ ] Advanced analytics and reporting

## Support

For technical support or business inquiries:
- Create an issue in the project repository
- Contact development team for enterprise licensing
- Documentation and tutorials available in `/docs` folder

## License

This project is proprietary software designed for commercial use in B2B and B2G contracts. Contact the development team for licensing information.

---

**CryptoID** - Secure, Reliable, Professional Identity Verification
