import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import '../models/person.dart';
import '../models/identification_card.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._init();
  static Database? _database;
  static final Map<String, Person> _webPersonStorage = {};

  DatabaseService._init();

  Future<Database> get database async {
    if (kIsWeb) {
      throw UnsupportedError('Database not supported on web');
    }
    if (_database != null) return _database!;
    _database = await _initDB('crypto_id.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
    );
  }

  Future _createDB(Database db, int version) async {
    // Create persons table
    await db.execute('''
      CREATE TABLE persons (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT NOT NULL,
        id_number TEXT NOT NULL UNIQUE,
        id_type TEXT NOT NULL,
        department TEXT,
        position TEXT,
        company TEXT,
        address TEXT,
        emergency_contact TEXT,
        photo_path TEXT,
        created_at TEXT NOT NULL
      )
    ''');

    // Create identification_cards table
    await db.execute('''
      CREATE TABLE identification_cards (
        id TEXT PRIMARY KEY,
        person_id TEXT NOT NULL,
        card_type TEXT NOT NULL,
        organization_name TEXT NOT NULL,
        position TEXT NOT NULL,
        department TEXT,
        student_id TEXT,
        employee_id TEXT,
        issue_date TEXT NOT NULL,
        expiry_date TEXT NOT NULL,
        crypto_signature TEXT NOT NULL,
        qr_code_data TEXT NOT NULL,
        logo_path TEXT,
        custom_fields TEXT,
        FOREIGN KEY (person_id) REFERENCES persons (id)
      )
    ''');

    // Create scan_history table
    await db.execute('''
      CREATE TABLE scan_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        card_id TEXT,
        scanned_data TEXT NOT NULL,
        scan_timestamp TEXT NOT NULL,
        verification_status TEXT NOT NULL
      )
    ''');
  }

  // Person CRUD operations
  Future<String> insertPerson(Person person) async {
    final id = person.id ?? DateTime.now().millisecondsSinceEpoch.toString();
    final personWithId = Person(
      id: id,
      name: person.name,
      email: person.email,
      phone: person.phone,
      idNumber: person.idNumber,
      idType: person.idType,
      department: person.department,
      position: person.position,
      company: person.company,
      address: person.address,
      emergencyContact: person.emergencyContact,
      photoPath: person.photoPath,
      createdAt: person.createdAt,
    );

    if (kIsWeb) {
      // Store in memory for web
      _webPersonStorage[id] = personWithId;
      return id;
    }

    final db = await instance.database;
    await db.insert('persons', personWithId.toMap());
    return id;
  }

  Future<Person?> getPerson(String id) async {
    if (kIsWeb) {
      return _webPersonStorage[id];
    }

    final db = await instance.database;
    final maps = await db.query(
      'persons',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Person.fromMap(maps.first);
    }
    return null;
  }

  Future<Person?> getPersonByIdNumber(String idNumber) async {
    if (kIsWeb) {
      // Search in memory storage for web
      for (final person in _webPersonStorage.values) {
        if (person.idNumber == idNumber) {
          return person;
        }
      }
      return null;
    }

    final db = await instance.database;
    final maps = await db.query(
      'persons',
      where: 'id_number = ?',
      whereArgs: [idNumber],
    );

    if (maps.isNotEmpty) {
      return Person.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Person>> getAllPersons() async {
    if (kIsWeb) {
      final persons = _webPersonStorage.values.toList();
      persons.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return persons;
    }

    final db = await instance.database;
    final maps = await db.query('persons', orderBy: 'created_at DESC');
    return maps.map((map) => Person.fromMap(map)).toList();
  }

  Future<void> updatePerson(Person person) async {
    if (kIsWeb) {
      if (person.id != null) {
        _webPersonStorage[person.id!] = person;
      }
      return;
    }

    final db = await instance.database;
    await db.update(
      'persons',
      person.toMap(),
      where: 'id = ?',
      whereArgs: [person.id],
    );
  }

  Future<void> deletePerson(String id) async {
    if (kIsWeb) {
      _webPersonStorage.remove(id);
      return;
    }

    final db = await instance.database;
    await db.delete(
      'persons',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Card CRUD operations
  Future<String> insertCard(IdentificationCard card) async {
    final db = await instance.database;
    
    // First ensure person exists
    await insertPerson(card.person);
    
    // Then insert card
    await db.insert('identification_cards', card.toMap());
    return card.id;
  }

  Future<List<IdentificationCard>> getAllCards() async {
    final db = await instance.database;
    final maps = await db.rawQuery('''
      SELECT c.*, p.*
      FROM identification_cards c
      JOIN persons p ON c.person_id = p.id
      ORDER BY c.issue_date DESC
    ''');

    List<IdentificationCard> cards = [];
    for (var map in maps) {
      final person = Person.fromMap({
        'id': map['person_id'],
        'first_name': map['first_name'],
        'last_name': map['last_name'],
        'email': map['email'],
        'phone': map['phone'],
        'photo_path': map['photo_path'],
        'date_of_birth': map['date_of_birth'],
        'address': map['address'],
        'created_at': map['created_at'],
      });

      final card = IdentificationCard(
        id: map['id'] as String,
        person: person,
        cardType: CardType.values.firstWhere((e) => e.name == map['card_type']),
        organizationName: map['organization_name'] as String,
        position: map['position'] as String,
        department: map['department'] as String?,
        studentId: map['student_id'] as String?,
        employeeId: map['employee_id'] as String?,
        issueDate: DateTime.parse(map['issue_date'] as String),
        expiryDate: DateTime.parse(map['expiry_date'] as String),
        cryptoSignature: map['crypto_signature'] as String,
        qrCodeData: map['qr_code_data'] as String,
        logoPath: map['logo_path'] as String?,
      );
      
      cards.add(card);
    }
    
    return cards;
  }

  // Scan history
  Future<void> insertScanHistory({
    required String? cardId,
    required String scannedData,
    required String verificationStatus,
  }) async {
    final db = await instance.database;
    await db.insert('scan_history', {
      'card_id': cardId,
      'scanned_data': scannedData,
      'scan_timestamp': DateTime.now().toIso8601String(),
      'verification_status': verificationStatus,
    });
  }

  Future<void> close() async {
    final db = await instance.database;
    db.close();
  }
}
