@echo off
echo ========================================
echo CryptoID Flutter App Setup
echo ========================================
echo.

echo Step 1: Downloading Flutter SDK...
powershell -Command "Invoke-WebRequest -Uri 'https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.5-stable.zip' -OutFile 'flutter_sdk.zip'"

echo Step 2: Extracting Flutter SDK...
powershell -Command "Expand-Archive -Path 'flutter_sdk.zip' -DestinationPath '.' -Force"

echo Step 3: Setting up Flutter path...
set FLUTTER_PATH=%CD%\flutter\bin
set PATH=%FLUTTER_PATH%;%PATH%

echo Step 4: Running Flutter doctor...
flutter\bin\flutter doctor

echo Step 5: Getting Flutter dependencies...
flutter\bin\flutter pub get

echo Step 6: Creating local.properties for Android...
echo flutter.sdk=%CD%\flutter > android\local.properties

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo To run the app:
echo 1. Connect an Android device or start an emulator
echo 2. Run: flutter\bin\flutter run
echo.
echo To build APK:
echo Run: flutter\bin\flutter build apk
echo.
pause
