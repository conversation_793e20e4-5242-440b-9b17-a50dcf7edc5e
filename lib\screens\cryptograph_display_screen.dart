import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/person.dart';
import '../services/crypto_service.dart';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;

class CryptographDisplayScreen extends StatefulWidget {
  final Person person;
  final File? imageFile;
  final Uint8List? webImage;

  const CryptographDisplayScreen({
    super.key,
    required this.person,
    this.imageFile,
    this.webImage,
  });

  @override
  State<CryptographDisplayScreen> createState() => _CryptographDisplayScreenState();
}

class _CryptographDisplayScreenState extends State<CryptographDisplayScreen> {
  String? _cryptographData;
  String? _digitalSignature;
  bool _isGenerating = true;

  @override
  void initState() {
    super.initState();
    _generateCryptograph();
  }

  Future<void> _generateCryptograph() async {
    try {
      // Convert image to base64
      String imageBase64 = '';
      if (widget.webImage != null) {
        imageBase64 = base64Encode(widget.webImage!);
      } else if (widget.imageFile != null) {
        final bytes = await widget.imageFile!.readAsBytes();
        imageBase64 = base64Encode(bytes);
      }

      // Create comprehensive data structure
      final personData = {
        'name': widget.person.name,
        'email': widget.person.email,
        'phone': widget.person.phone,
        'idNumber': widget.person.idNumber,
        'idType': widget.person.idType,
        'department': widget.person.department,
        'position': widget.person.position,
        'company': widget.person.company,
        'address': widget.person.address,
        'emergencyContact': widget.person.emergencyContact,
        'photo': imageBase64,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'issuer': 'CryptoID System',
        'version': '1.0',
      };

      // Convert to JSON
      final jsonData = jsonEncode(personData);
      
      // Encrypt the data
      final encryptedData = CryptoService.encryptData(jsonData);
      
      // Generate digital signature
      final signature = CryptoService.generateSignature(jsonData);
      
      // Create final cryptograph structure
      final cryptograph = {
        'encrypted_data': encryptedData,
        'signature': signature,
        'algorithm': 'AES-256-GCM',
        'hash_algorithm': 'SHA-256',
        'created_at': DateTime.now().toIso8601String(),
      };

      setState(() {
        _cryptographData = base64Encode(utf8.encode(jsonEncode(cryptograph)));
        _digitalSignature = signature;
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _isGenerating = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error generating cryptograph: $e')),
        );
      }
    }
  }

  Future<void> _copyCryptograph() async {
    if (_cryptographData != null) {
      await Clipboard.setData(ClipboardData(text: _cryptographData!));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cryptograph copied to clipboard!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _saveCryptograph() async {
    if (_cryptographData == null) return;

    try {
      if (kIsWeb) {
        // For web, we'll create a downloadable file
        final bytes = utf8.encode(_cryptographData!);
        final blob = html.Blob([bytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.document.createElement('a') as html.AnchorElement
          ..href = url
          ..style.display = 'none'
          ..download = 'cryptograph_${widget.person.idNumber}_${DateTime.now().millisecondsSinceEpoch}.txt';
        html.document.body?.children.add(anchor);
        anchor.click();
        html.document.body?.children.remove(anchor);
        html.Url.revokeObjectUrl(url);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cryptograph downloaded successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // For mobile, show success message (would implement file saving in real app)
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cryptograph saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving cryptograph: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text(
          'Generated Cryptograph',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[800],
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
            tooltip: 'Home',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue[800]!,
              Colors.blue[600]!,
              Colors.grey[100]!,
            ],
          ),
        ),
        child: SafeArea(
          child: _isGenerating
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: Colors.white),
                      SizedBox(height: 20),
                      Text(
                        'Generating Cryptograph...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text(
                        'Encrypting data with AES-256\nGenerating digital signature...',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      // Success Header
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 50,
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'Cryptograph Generated Successfully!',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'ID: ${widget.person.idNumber}',
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      
                      // Person Info Card
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                // Photo
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(color: Colors.grey[300]!),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(13),
                                    child: widget.webImage != null
                                        ? Image.memory(
                                            widget.webImage!,
                                            fit: BoxFit.cover,
                                          )
                                        : widget.imageFile != null
                                            ? Image.file(
                                                widget.imageFile!,
                                                fit: BoxFit.cover,
                                              )
                                            : Icon(
                                                Icons.person,
                                                size: 40,
                                                color: Colors.grey[400],
                                              ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        widget.person.name,
                                        style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '${widget.person.idType} ID',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: Colors.blue[800],
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'ID: ${widget.person.idNumber}',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      
                      // Cryptograph Data
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.security,
                                  color: Colors.blue[800],
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Encrypted Cryptograph',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: Text(
                                _cryptographData ?? 'Error generating cryptograph',
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                                maxLines: 8,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Icon(
                                  Icons.verified,
                                  color: Colors.green[600],
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'AES-256 Encrypted • SHA-256 Signed',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      
                      // Action Buttons
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _copyCryptograph,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue[800],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.copy),
                                  SizedBox(width: 8),
                                  Text('Copy'),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _saveCryptograph,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.download),
                                  SizedBox(width: 8),
                                  Text('Save'),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}
