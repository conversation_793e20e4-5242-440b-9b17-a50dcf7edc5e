@echo off
echo ========================================
echo    CryptoID Portable Flutter Setup
echo ========================================
echo.

:: Create local flutter directory
if not exist "flutter_portable" mkdir "flutter_portable"
cd flutter_portable

echo [1/4] Downloading Flutter SDK (Portable)...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.5-stable.zip' -OutFile 'flutter.zip' -UseBasicParsing}"

echo [2/4] Extracting Flutter...
powershell -Command "Expand-Archive -Path 'flutter.zip' -DestinationPath '.' -Force"

echo [3/4] Setting up local environment...
set FLUTTER_ROOT=%CD%\flutter
set PATH=%FLUTTER_ROOT%\bin;%PATH%

echo [4/4] Testing Flutter installation...
%FLUTTER_ROOT%\bin\flutter.bat --version

echo.
echo ========================================
echo    Portable Setup Complete!
echo ========================================
echo.
echo Flutter is now available at: %FLUTTER_ROOT%
echo.
echo To use Flutter in this session, run:
echo   set PATH=%FLUTTER_ROOT%\bin;%%PATH%%
echo   flutter doctor
echo.
echo To build your app:
echo   cd ..
echo   flutter pub get
echo   flutter run
echo.
pause
