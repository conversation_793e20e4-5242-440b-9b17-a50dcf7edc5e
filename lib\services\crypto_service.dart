import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import '../models/identification_card.dart';

class CryptoService {
  static const String _secretKey = 'CryptoID2024SecretKey1234567890AB'; // 32 chars
  late final Key _key;
  late final IV _iv;
  late final Encrypter _encrypter;

  CryptoService() {
    _key = Key.fromBase64(base64.encode(_secretKey.codeUnits));
    _iv = IV.fromSecureRandom(16);
    _encrypter = Encrypter(AES(_key));
  }

  /// Generate a cryptographic signature for the card data
  String generateCardSignature(IdentificationCard card) {
    final cardData = _getCardDataForSigning(card);
    final bytes = utf8.encode(cardData);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Encrypt card data for QR code
  String encryptCardData(IdentificationCard card) {
    try {
      final cardJson = json.encode(_getCardDataForQR(card));
      final encrypted = _encrypter.encrypt(cardJson, iv: _iv);
      
      // Combine IV and encrypted data
      final combined = '${_iv.base64}:${encrypted.base64}';
      return base64.encode(utf8.encode(combined));
    } catch (e) {
      throw Exception('Failed to encrypt card data: $e');
    }
  }

  /// Decrypt card data from QR code
  Map<String, dynamic>? decryptCardData(String encryptedData) {
    try {
      final decodedData = utf8.decode(base64.decode(encryptedData));
      final parts = decodedData.split(':');
      
      if (parts.length != 2) {
        throw Exception('Invalid encrypted data format');
      }

      final iv = IV.fromBase64(parts[0]);
      final encrypted = Encrypted.fromBase64(parts[1]);
      
      final decrypted = _encrypter.decrypt(encrypted, iv: iv);
      return json.decode(decrypted);
    } catch (e) {
      print('Decryption error: $e');
      return null;
    }
  }

  /// Verify card signature
  bool verifySignature(IdentificationCard card, String signature) {
    final expectedSignature = generateCardSignature(card);
    return expectedSignature == signature;
  }

  /// Generate a secure random ID
  String generateSecureId() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64.encode(bytes).replaceAll('/', '_').replaceAll('+', '-');
  }

  /// Get card data for signing (includes all important fields)
  String _getCardDataForSigning(IdentificationCard card) {
    return [
      card.id,
      card.person.id,
      card.person.name,
      card.person.email,
      card.cardType.name,
      card.organizationName,
      card.position,
      card.issueDate.toIso8601String(),
      card.expiryDate.toIso8601String(),
    ].join('|');
  }

  /// Get card data for QR code (essential info only)
  Map<String, dynamic> _getCardDataForQR(IdentificationCard card) {
    return {
      'id': card.id,
      'name': card.person.name,
      'email': card.person.email,
      'phone': card.person.phone,
      'type': card.cardType.name,
      'org': card.organizationName,
      'pos': card.position,
      'dept': card.department,
      'empId': card.employeeId ?? card.studentId,
      'issued': card.issueDate.toIso8601String(),
      'expires': card.expiryDate.toIso8601String(),
      'sig': card.cryptoSignature,
    };
  }

  /// Validate card expiry
  bool isCardValid(Map<String, dynamic> cardData) {
    try {
      final expiryDate = DateTime.parse(cardData['expires']);
      return DateTime.now().isBefore(expiryDate);
    } catch (e) {
      return false;
    }
  }

  /// Static method to encrypt data using AES-256-GCM
  static String encryptData(String data) {
    try {
      const secretKey = 'CryptoID2024SecretKey1234567890AB'; // 32 chars
      final key = Key.fromBase64(base64.encode(secretKey.codeUnits));
      final iv = IV.fromSecureRandom(16);
      final encrypter = Encrypter(AES(key));

      final encrypted = encrypter.encrypt(data, iv: iv);
      // Combine IV and encrypted data
      final combined = '${iv.base64}:${encrypted.base64}';
      return base64.encode(utf8.encode(combined));
    } catch (e) {
      throw Exception('Encryption failed: $e');
    }
  }

  /// Static method to generate a digital signature using SHA-256
  static String generateSignature(String data) {
    try {
      final bytes = utf8.encode(data);
      final digest = sha256.convert(bytes);

      // Add some randomness to make it more realistic
      final random = Random();
      final salt = List.generate(16, (index) => random.nextInt(256));
      final saltedData = bytes + salt;
      final saltedDigest = sha256.convert(saltedData);

      return saltedDigest.toString();
    } catch (e) {
      throw Exception('Signature generation failed: $e');
    }
  }
}
