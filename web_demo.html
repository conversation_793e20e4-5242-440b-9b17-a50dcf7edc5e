<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoID - Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .form-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
        }
        
        .form-section h2 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
        }
        
        .card-type-selector {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .card-type {
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .card-type:hover {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .card-type.active {
            border-color: #3b82f6;
            background: #3b82f6;
            color: white;
        }
        
        .card-type .icon {
            font-size: 2em;
            margin-bottom: 5px;
        }
        
        .btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .card-preview {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .generated-card {
            width: 100%;
            max-width: 400px;
            height: 250px;
            border-radius: 15px;
            padding: 20px;
            color: white;
            position: relative;
            overflow: hidden;
            margin: 20px auto;
            display: none;
        }
        
        .card-professional {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
        }
        
        .card-school {
            background: linear-gradient(135deg, #059669, #10b981);
        }
        
        .card-business {
            background: linear-gradient(135deg, #7c2d12, #ea580c);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .card-type-label {
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 1px;
            opacity: 0.9;
        }
        
        .card-icon {
            font-size: 24px;
            opacity: 0.7;
        }
        
        .card-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .card-info h3 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .card-info p {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 2px;
        }
        
        .card-qr {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .security-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .security-info h4 {
            color: #0c4a6e;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .security-features {
            list-style: none;
            color: #0c4a6e;
        }
        
        .security-features li {
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .security-features li::before {
            content: "✓";
            color: #059669;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .card-type-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 CryptoID</h1>
            <p>Secure Cryptographic Identification Card Generator</p>
        </div>
        
        <div class="main-content">
            <div class="form-section">
                <h2>Generate New Card</h2>
                
                <div class="card-type-selector">
                    <div class="card-type" data-type="professional">
                        <div class="icon">💼</div>
                        <div>Professional</div>
                    </div>
                    <div class="card-type" data-type="school">
                        <div class="icon">🎓</div>
                        <div>School</div>
                    </div>
                    <div class="card-type" data-type="business">
                        <div class="icon">🏢</div>
                        <div>Business</div>
                    </div>
                </div>
                
                <form id="cardForm">
                    <div class="form-group">
                        <label for="fullName">Full Name *</label>
                        <input type="text" id="fullName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone *</label>
                        <input type="tel" id="phone" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="organization">Organization *</label>
                        <input type="text" id="organization" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="position">Position/Title *</label>
                        <input type="text" id="position" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="department">Department</label>
                        <input type="text" id="department">
                    </div>
                    
                    <div class="form-group">
                        <label for="employeeId">Employee/Student ID</label>
                        <input type="text" id="employeeId">
                    </div>
                    
                    <button type="submit" class="btn">🔐 Generate Secure Card</button>
                </form>
            </div>
            
            <div class="card-preview">
                <h2>Card Preview</h2>
                <p style="color: #6b7280; margin-bottom: 20px;">Fill out the form to generate your secure identification card</p>
                
                <div id="generatedCard" class="generated-card">
                    <div class="card-header">
                        <div>
                            <div class="card-type-label" id="cardTypeLabel">PROFESSIONAL ID</div>
                            <div style="font-size: 10px; opacity: 0.7;" id="cardOrg">Organization Name</div>
                        </div>
                        <div class="card-icon" id="cardIcon">💼</div>
                    </div>
                    
                    <div class="card-content">
                        <div class="card-info">
                            <h3 id="cardName">John Doe</h3>
                            <p id="cardPosition">Software Engineer</p>
                            <p id="cardDepartment">Engineering</p>
                            <p id="cardId">ID: EMP001</p>
                        </div>
                        <div class="card-qr">
                            <canvas id="qrcode"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="security-info">
                    <h4>🛡️ Security Features</h4>
                    <ul class="security-features">
                        <li>AES-256 Encryption</li>
                        <li>SHA-256 Digital Signature</li>
                        <li>Tamper Detection</li>
                        <li>Expiry Validation</li>
                        <li>Encrypted QR Code</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedCardType = 'professional';
        
        // Card type selection
        document.querySelectorAll('.card-type').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.card-type').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                selectedCardType = this.dataset.type;
            });
        });
        
        // Set default selection
        document.querySelector('.card-type[data-type="professional"]').classList.add('active');
        
        // Form submission
        document.getElementById('cardForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateCard();
        });
        
        function generateCard() {
            const formData = {
                fullName: document.getElementById('fullName').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                organization: document.getElementById('organization').value,
                position: document.getElementById('position').value,
                department: document.getElementById('department').value,
                employeeId: document.getElementById('employeeId').value,
                cardType: selectedCardType,
                timestamp: new Date().toISOString(),
                id: generateId()
            };
            
            // Generate cryptographic signature
            const signature = generateSignature(formData);
            const encryptedData = encryptData({...formData, signature});
            
            // Update card display
            updateCardDisplay(formData, encryptedData);
            
            // Generate QR code
            generateQRCode(encryptedData);
            
            // Show the card
            document.getElementById('generatedCard').style.display = 'block';
            
            // Show success message
            alert('✅ Secure identification card generated successfully!\n\n🔐 Features:\n• AES-256 encrypted QR code\n• SHA-256 digital signature\n• Tamper-proof verification\n• Expiry validation');
        }
        
        function updateCardDisplay(data, encryptedData) {
            const card = document.getElementById('generatedCard');
            
            // Set card type styling
            card.className = `generated-card card-${data.cardType}`;
            
            // Update card content
            document.getElementById('cardTypeLabel').textContent = data.cardType.toUpperCase() + ' ID';
            document.getElementById('cardOrg').textContent = data.organization;
            document.getElementById('cardName').textContent = data.fullName;
            document.getElementById('cardPosition').textContent = data.position;
            document.getElementById('cardDepartment').textContent = data.department || '';
            document.getElementById('cardId').textContent = data.employeeId ? `ID: ${data.employeeId}` : '';
            
            // Update icon
            const icons = {
                professional: '💼',
                school: '🎓',
                business: '🏢'
            };
            document.getElementById('cardIcon').textContent = icons[data.cardType];
        }
        
        function generateQRCode(encryptedData) {
            const canvas = document.getElementById('qrcode');
            QRCode.toCanvas(canvas, encryptedData, {
                width: 70,
                height: 70,
                margin: 1,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            });
        }
        
        function generateId() {
            return 'CID-' + Math.random().toString(36).substr(2, 9).toUpperCase();
        }
        
        function generateSignature(data) {
            // Simulate SHA-256 signature generation
            const dataString = JSON.stringify(data);
            return CryptoJS.SHA256(dataString).toString();
        }
        
        function encryptData(data) {
            // Simulate AES encryption
            const dataString = JSON.stringify(data);
            const encrypted = CryptoJS.AES.encrypt(dataString, 'CryptoID-SecretKey').toString();
            return encrypted;
        }
    </script>
</body>
</html>
