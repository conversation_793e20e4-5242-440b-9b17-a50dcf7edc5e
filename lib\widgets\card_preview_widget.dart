import 'package:flutter/material.dart';
import '../models/identification_card.dart';

class CardPreviewWidget extends StatelessWidget {
  final IdentificationCard card;

  const CardPreviewWidget({super.key, required this.card});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getCardColor(),
          child: Icon(
            _getCardIcon(),
            color: Colors.white,
          ),
        ),
        title: Text(
          card.person.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${card.cardTypeName} • ${card.organizationName}'),
            Text(
              card.position,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            Row(
              children: [
                Icon(
                  card.isExpired ? Icons.error : Icons.check_circle,
                  size: 12,
                  color: card.isExpired ? Colors.red : Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  card.isExpired ? 'Expired' : 'Valid',
                  style: TextStyle(
                    fontSize: 12,
                    color: card.isExpired ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _formatDate(card.issueDate),
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 4),
            Text(
              'Expires: ${_formatDate(card.expiryDate)}',
              style: const TextStyle(fontSize: 10, color: Colors.grey),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  Color _getCardColor() {
    switch (card.cardType) {
      case CardType.professional:
        return Colors.blue;
      case CardType.school:
        return Colors.green;
      case CardType.business:
        return Colors.orange;
    }
  }

  IconData _getCardIcon() {
    switch (card.cardType) {
      case CardType.professional:
        return Icons.work;
      case CardType.school:
        return Icons.school;
      case CardType.business:
        return Icons.business;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
