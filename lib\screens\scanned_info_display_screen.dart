import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class ScannedInfoDisplayScreen extends StatelessWidget {
  final Map<String, dynamic> personData;
  final Map<String, dynamic> cryptographData;

  const ScannedInfoDisplayScreen({
    super.key,
    required this.personData,
    required this.cryptographData,
  });

  Future<void> _copyInfo(BuildContext context) async {
    final infoText = '''
Name: ${personData['name']}
Email: ${personData['email']}
Phone: ${personData['phone']}
ID Number: ${personData['idNumber']}
ID Type: ${personData['idType']}
Department: ${personData['department'] ?? 'N/A'}
Position: ${personData['position'] ?? 'N/A'}
Company: ${personData['company'] ?? 'N/A'}
Address: ${personData['address'] ?? 'N/A'}
Emergency Contact: ${personData['emergencyContact'] ?? 'N/A'}
Issued: ${DateTime.fromMillisecondsSinceEpoch(personData['timestamp']).toString()}
Issuer: ${personData['issuer']}
''';

    await Clipboard.setData(ClipboardData(text: infoText));
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Information copied to clipboard!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final issuedDate = DateTime.fromMillisecondsSinceEpoch(personData['timestamp']);
    final createdDate = DateTime.parse(cryptographData['created_at']);
    
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text(
          'Scanned Information',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[800],
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
            tooltip: 'Home',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue[800]!,
              Colors.blue[600]!,
              Colors.grey[100]!,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                // Verification Status
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.verified,
                        color: Colors.white,
                        size: 50,
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'Cryptograph Verified!',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Signature Valid • Data Authentic',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                
                // Person Information Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.person,
                            color: Colors.blue[800],
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Personal Information',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      
                      _buildInfoRow('Name', personData['name']),
                      _buildInfoRow('Email', personData['email']),
                      _buildInfoRow('Phone', personData['phone']),
                      _buildInfoRow('ID Number', personData['idNumber']),
                      _buildInfoRow('ID Type', personData['idType']),
                      
                      if (personData['department'] != null && personData['department'].isNotEmpty)
                        _buildInfoRow('Department', personData['department']),
                      
                      if (personData['position'] != null && personData['position'].isNotEmpty)
                        _buildInfoRow('Position', personData['position']),
                      
                      if (personData['company'] != null && personData['company'].isNotEmpty)
                        _buildInfoRow('Company', personData['company']),
                      
                      if (personData['address'] != null && personData['address'].isNotEmpty)
                        _buildInfoRow('Address', personData['address']),
                      
                      if (personData['emergencyContact'] != null && personData['emergencyContact'].isNotEmpty)
                        _buildInfoRow('Emergency Contact', personData['emergencyContact']),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                
                // Cryptograph Details
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.security,
                            color: Colors.blue[800],
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Cryptograph Details',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      
                      _buildInfoRow('Algorithm', cryptographData['algorithm']),
                      _buildInfoRow('Hash Algorithm', cryptographData['hash_algorithm']),
                      _buildInfoRow('Created', createdDate.toString().split('.')[0]),
                      _buildInfoRow('Issued', issuedDate.toString().split('.')[0]),
                      _buildInfoRow('Issuer', personData['issuer']),
                      _buildInfoRow('Version', personData['version']),
                      
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Colors.green[200]!),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.green[600],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            const Expanded(
                              child: Text(
                                'Digital signature verified successfully',
                                style: TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                
                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _copyInfo(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[800],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.copy),
                            SizedBox(width: 8),
                            Text('Copy Info'),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.popUntil(context, (route) => route.isFirst);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.home),
                            SizedBox(width: 8),
                            Text('Home'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
