import 'person.dart';

enum CardType {
  professional,
  school,
  business,
}

class IdentificationCard {
  final String id;
  final Person person;
  final CardType cardType;
  final String organizationName;
  final String position;
  final String? department;
  final String? studentId;
  final String? employeeId;
  final DateTime issueDate;
  final DateTime expiryDate;
  final String cryptoSignature;
  final String qrCodeData;
  final String? logoPath;
  final Map<String, dynamic> customFields;

  IdentificationCard({
    required this.id,
    required this.person,
    required this.cardType,
    required this.organizationName,
    required this.position,
    this.department,
    this.studentId,
    this.employeeId,
    required this.issueDate,
    required this.expiryDate,
    required this.cryptoSignature,
    required this.qrCodeData,
    this.logoPath,
    this.customFields = const {},
  });

  String get cardTypeName {
    switch (cardType) {
      case CardType.professional:
        return 'Professional ID';
      case CardType.school:
        return 'School ID';
      case CardType.business:
        return 'Business Card';
    }
  }

  bool get isExpired => DateTime.now().isAfter(expiryDate);

  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'person': person.toJson(),
      'cardType': cardType.name,
      'organizationName': organizationName,
      'position': position,
      'department': department,
      'studentId': studentId,
      'employeeId': employeeId,
      'issueDate': issueDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'cryptoSignature': cryptoSignature,
      'qrCodeData': qrCodeData,
      'logoPath': logoPath,
      'customFields': customFields,
    };
  }

  factory IdentificationCard.fromJson(Map<String, dynamic> json) {
    return IdentificationCard(
      id: json['id'],
      person: Person.fromJson(json['person']),
      cardType: CardType.values.firstWhere((e) => e.name == json['cardType']),
      organizationName: json['organizationName'],
      position: json['position'],
      department: json['department'],
      studentId: json['studentId'],
      employeeId: json['employeeId'],
      issueDate: DateTime.parse(json['issueDate']),
      expiryDate: DateTime.parse(json['expiryDate']),
      cryptoSignature: json['cryptoSignature'],
      qrCodeData: json['qrCodeData'],
      logoPath: json['logoPath'],
      customFields: Map<String, dynamic>.from(json['customFields'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'person_id': person.id,
      'card_type': cardType.name,
      'organization_name': organizationName,
      'position': position,
      'department': department,
      'student_id': studentId,
      'employee_id': employeeId,
      'issue_date': issueDate.toIso8601String(),
      'expiry_date': expiryDate.toIso8601String(),
      'crypto_signature': cryptoSignature,
      'qr_code_data': qrCodeData,
      'logo_path': logoPath,
      'custom_fields': customFields.isNotEmpty ? customFields.toString() : null,
    };
  }
}
