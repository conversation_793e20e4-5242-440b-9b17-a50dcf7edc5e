@echo off
echo ========================================
echo    CryptoID Flutter Development Setup
echo ========================================
echo.

:: Create development directory
if not exist "C:\dev" mkdir "C:\dev"
cd /d "C:\dev"

echo [1/6] Downloading Flutter SDK...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.5-stable.zip' -OutFile 'flutter_windows.zip' -UseBasicParsing}"

echo [2/6] Extracting Flutter SDK...
powershell -Command "Expand-Archive -Path 'flutter_windows.zip' -DestinationPath '.' -Force"

echo [3/6] Setting up Flutter PATH...
set FLUTTER_PATH=C:\dev\flutter\bin
setx PATH "%PATH%;%FLUTTER_PATH%" /M

echo [4/6] Downloading Android Command Line Tools...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip' -OutFile 'android-tools.zip' -UseBasicParsing}"

echo [5/6] Setting up Android SDK...
if not exist "C:\dev\android-sdk" mkdir "C:\dev\android-sdk"
powershell -Command "Expand-Archive -Path 'android-tools.zip' -DestinationPath 'C:\dev\android-sdk' -Force"

:: Set Android environment variables
setx ANDROID_HOME "C:\dev\android-sdk" /M
setx PATH "%PATH%;C:\dev\android-sdk\cmdline-tools\bin;C:\dev\android-sdk\platform-tools" /M

echo [6/6] Installing Android SDK components...
cd /d "C:\dev\android-sdk\cmdline-tools\bin"
echo y | sdkmanager.bat --sdk_root=C:\dev\android-sdk "platform-tools" "platforms;android-34" "build-tools;34.0.0"

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo Please restart your command prompt and run:
echo   flutter doctor
echo.
echo Then navigate to your project and run:
echo   flutter pub get
echo   flutter run
echo.
pause
