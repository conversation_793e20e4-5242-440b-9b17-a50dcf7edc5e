import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../models/identification_card.dart';

class CardWidget extends StatelessWidget {
  final IdentificationCard card;
  final double? width;
  final double? height;

  const CardWidget({
    super.key,
    required this.card,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final cardWidth = width ?? 350.0;
    final cardHeight = height ?? 220.0;
    
    return Container(
      width: cardWidth,
      height: cardHeight,
      decoration: BoxDecoration(
        gradient: _getCardGradient(),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                backgroundBlendMode: BlendMode.overlay,
              ),
              child: CustomPaint(
                painter: CardPatternPainter(),
              ),
            ),
          ),
          
          // Card content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          card.cardTypeName.toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                          ),
                        ),
                        Text(
                          card.organizationName,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                    Icon(
                      _getCardIcon(),
                      color: Colors.white70,
                      size: 24,
                    ),
                  ],
                ),
                
                const Spacer(),
                
                // Main content
                Row(
                  children: [
                    // Left side - Person info
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Photo placeholder
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.white30),
                            ),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white70,
                              size: 30,
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // Name
                          Text(
                            card.person.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // Position
                          Text(
                            card.position,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          // Department (if available)
                          if (card.department != null) ...[
                            Text(
                              card.department!,
                              style: const TextStyle(
                                color: Colors.white60,
                                fontSize: 10,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Right side - QR Code
                    Container(
                      width: 80,
                      height: 80,
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: QrImageView(
                        data: card.qrCodeData,
                        version: QrVersions.auto,
                        size: 72,
                        backgroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Footer
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (card.employeeId != null || card.studentId != null)
                          Text(
                            'ID: ${card.employeeId ?? card.studentId}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 10,
                            ),
                          ),
                        Text(
                          'Expires: ${_formatDate(card.expiryDate)}',
                          style: const TextStyle(
                            color: Colors.white60,
                            fontSize: 9,
                          ),
                        ),
                      ],
                    ),
                    
                    // Security indicator
                    Row(
                      children: [
                        Icon(
                          Icons.security,
                          color: Colors.white70,
                          size: 12,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'SECURED',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  LinearGradient _getCardGradient() {
    switch (card.cardType) {
      case CardType.professional:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1E3A8A), // Blue 800
            Color(0xFF3B82F6), // Blue 500
          ],
        );
      case CardType.school:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF059669), // Emerald 600
            Color(0xFF10B981), // Emerald 500
          ],
        );
      case CardType.business:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF7C2D12), // Orange 800
            Color(0xFFEA580C), // Orange 600
          ],
        );
    }
  }

  IconData _getCardIcon() {
    switch (card.cardType) {
      case CardType.professional:
        return Icons.work;
      case CardType.school:
        return Icons.school;
      case CardType.business:
        return Icons.business;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year.toString().substring(2)}';
  }
}

class CardPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw geometric pattern
    final path = Path();
    
    // Create a subtle geometric pattern
    for (int i = 0; i < 5; i++) {
      final y = size.height * (i / 4);
      path.moveTo(0, y);
      path.lineTo(size.width, y);
    }
    
    for (int i = 0; i < 8; i++) {
      final x = size.width * (i / 7);
      path.moveTo(x, 0);
      path.lineTo(x, size.height);
    }
    
    canvas.drawPath(path, paint);
    
    // Add some circles for decoration
    final circlePaint = Paint()
      ..color = Colors.white.withOpacity(0.05)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.2),
      30,
      circlePaint,
    );
    
    canvas.drawCircle(
      Offset(size.width * 0.9, size.height * 0.8),
      20,
      circlePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
