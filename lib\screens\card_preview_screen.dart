import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import '../models/identification_card.dart';
import '../widgets/card_widget.dart';

class CardPreviewScreen extends StatelessWidget {
  final IdentificationCard card;

  const CardPreviewScreen({super.key, required this.card});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Card Preview'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareCard(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Success message
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green.shade700),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Card Generated Successfully!',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                        Text(
                          'Your ${card.cardTypeName} has been created with cryptographic security.',
                          style: TextStyle(color: Colors.green.shade600),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Card Preview
            const Text(
              'Card Preview',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Center(
              child: CardWidget(card: card),
            ),
            
            const SizedBox(height: 24),
            
            // QR Code Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'QR Code',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This QR code contains encrypted information about the card holder. It can be scanned to verify the authenticity of the card.',
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: QrImageView(
                          data: card.qrCodeData,
                          version: QrVersions.auto,
                          size: 200.0,
                          backgroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Card Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Card Information',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    _buildInfoRow('Card ID', card.id),
                    _buildInfoRow('Type', card.cardTypeName),
                    _buildInfoRow('Holder', card.person.fullName),
                    _buildInfoRow('Email', card.person.email),
                    _buildInfoRow('Phone', card.person.phone),
                    _buildInfoRow('Organization', card.organizationName),
                    _buildInfoRow('Position', card.position),
                    if (card.department != null)
                      _buildInfoRow('Department', card.department!),
                    if (card.employeeId != null)
                      _buildInfoRow('Employee ID', card.employeeId!),
                    if (card.studentId != null)
                      _buildInfoRow('Student ID', card.studentId!),
                    _buildInfoRow('Issue Date', _formatDate(card.issueDate)),
                    _buildInfoRow('Expiry Date', _formatDate(card.expiryDate)),
                    _buildInfoRow('Status', card.isExpired ? 'Expired' : 'Valid'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Security Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.security, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        const Text(
                          'Security Features',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildSecurityFeature(
                      'Cryptographic Signature',
                      'Each card has a unique digital signature that cannot be forged.',
                      Icons.verified_user,
                    ),
                    _buildSecurityFeature(
                      'Encrypted QR Code',
                      'Card data is encrypted using AES-256 encryption.',
                      Icons.lock,
                    ),
                    _buildSecurityFeature(
                      'Tamper Detection',
                      'Any modification to the card data will be detected during verification.',
                      Icons.shield,
                    ),
                    _buildSecurityFeature(
                      'Expiry Validation',
                      'Cards automatically become invalid after the expiry date.',
                      Icons.schedule,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _shareCard(context),
                    icon: const Icon(Icons.share),
                    label: const Text('Share Card'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
                    icon: const Icon(Icons.home),
                    label: const Text('Back to Home'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityFeature(String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: Colors.green.shade600, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  description,
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _shareCard(BuildContext context) {
    final text = '''
CryptoID Card - ${card.cardTypeName}

Name: ${card.person.fullName}
Organization: ${card.organizationName}
Position: ${card.position}
Valid until: ${_formatDate(card.expiryDate)}

This card is secured with cryptographic technology.
Scan the QR code to verify authenticity.
''';

    Share.share(text, subject: 'CryptoID Card - ${card.person.fullName}');
  }
}
