import 'package:flutter/foundation.dart';
import '../models/identification_card.dart';
import '../models/person.dart';
import '../services/database_service.dart';
import '../services/crypto_service.dart';

class CardProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;
  final CryptoService _cryptoService = CryptoService();
  
  List<IdentificationCard> _cards = [];
  bool _isLoading = false;
  String? _error;

  List<IdentificationCard> get cards => _cards;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadCards() async {
    _setLoading(true);
    try {
      _cards = await _databaseService.getAllCards();
      _error = null;
    } catch (e) {
      _error = 'Failed to load cards: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<IdentificationCard?> createCard({
    required Person person,
    required CardType cardType,
    required String organizationName,
    required String position,
    String? department,
    String? studentId,
    String? employeeId,
    required DateTime expiryDate,
    String? logoPath,
    Map<String, dynamic> customFields = const {},
  }) async {
    _setLoading(true);
    try {
      final cardId = _cryptoService.generateSecureId();
      final issueDate = DateTime.now();

      // Create temporary card for signature generation
      final tempCard = IdentificationCard(
        id: cardId,
        person: person,
        cardType: cardType,
        organizationName: organizationName,
        position: position,
        department: department,
        studentId: studentId,
        employeeId: employeeId,
        issueDate: issueDate,
        expiryDate: expiryDate,
        cryptoSignature: '', // Will be generated
        qrCodeData: '', // Will be generated
        logoPath: logoPath,
        customFields: customFields,
      );

      // Generate signature and QR data
      final signature = _cryptoService.generateSignature(tempCard);
      final qrData = _cryptoService.encryptCardData(tempCard);

      // Create final card with signature and QR data
      final card = IdentificationCard(
        id: cardId,
        person: person,
        cardType: cardType,
        organizationName: organizationName,
        position: position,
        department: department,
        studentId: studentId,
        employeeId: employeeId,
        issueDate: issueDate,
        expiryDate: expiryDate,
        cryptoSignature: signature,
        qrCodeData: qrData,
        logoPath: logoPath,
        customFields: customFields,
      );

      // Save to database
      await _databaseService.insertCard(card);
      
      // Add to local list
      _cards.insert(0, card);
      _error = null;
      
      notifyListeners();
      return card;
    } catch (e) {
      _error = 'Failed to create card: $e';
      notifyListeners();
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<Map<String, dynamic>?> verifyCard(String qrData) async {
    try {
      final cardData = _cryptoService.decryptCardData(qrData);
      if (cardData == null) {
        await _databaseService.insertScanHistory(
          cardId: null,
          scannedData: qrData,
          verificationStatus: 'INVALID_FORMAT',
        );
        return null;
      }

      // Check if card is still valid (not expired)
      final isValid = _cryptoService.isCardValid(cardData);
      final status = isValid ? 'VALID' : 'EXPIRED';

      await _databaseService.insertScanHistory(
        cardId: cardData['id'],
        scannedData: qrData,
        verificationStatus: status,
      );

      return {
        ...cardData,
        'isValid': isValid,
        'verificationStatus': status,
      };
    } catch (e) {
      await _databaseService.insertScanHistory(
        cardId: null,
        scannedData: qrData,
        verificationStatus: 'ERROR',
      );
      return null;
    }
  }

  List<IdentificationCard> getCardsByType(CardType type) {
    return _cards.where((card) => card.cardType == type).toList();
  }

  List<IdentificationCard> getExpiredCards() {
    return _cards.where((card) => card.isExpired).toList();
  }

  List<IdentificationCard> getValidCards() {
    return _cards.where((card) => !card.isExpired).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
