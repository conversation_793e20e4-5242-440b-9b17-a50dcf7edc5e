name: crypto_id
description: A Flutter app for generating and scanning cryptographic identification cards

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI Components
  cupertino_icons: ^1.0.2
  material_design_icons_flutter: ^7.0.7296
  
  # QR Code Generation and Scanning
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1
  mobile_scanner: ^3.5.6
  
  # Image Processing
  image: ^4.1.3
  flutter_image_compress: ^2.1.0
  
  # Cryptography
  crypto: ^3.0.3
  encrypt: ^5.0.1
  
  # File Operations
  path_provider: ^2.1.1
  share_plus: ^7.2.1
  
  # Database
  sqflite: ^2.3.0
  
  # State Management
  provider: ^6.1.1
  
  # Utilities
  uuid: ^4.2.1
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/templates/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
