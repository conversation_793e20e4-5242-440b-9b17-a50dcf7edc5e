@echo off
echo ========================================
echo    CRYPTOID FLUTTER PHONE SETUP
echo ========================================
echo Installing Flutter SDK and Android tools for phone debugging...
echo.

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Running with administrator privileges
) else (
    echo [!] This script requires administrator privileges
    echo [!] Please run as administrator to install Flutter
    pause
    exit /b 1
)

:: Create development directory
if not exist "C:\dev" mkdir "C:\dev"
cd /d "C:\dev"

echo [1/8] Downloading Flutter SDK...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Write-Host 'Downloading Flutter...'; Invoke-WebRequest -Uri 'https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.5-stable.zip' -OutFile 'flutter.zip' -UseBasicParsing; Write-Host 'Download complete'}"

echo [2/8] Extracting Flutter SDK...
powershell -Command "Write-Host 'Extracting Flutter...'; Expand-Archive -Path 'flutter.zip' -DestinationPath '.' -Force; Write-Host 'Extraction complete'"

echo [3/8] Setting Flutter PATH...
setx PATH "%PATH%;C:\dev\flutter\bin" /M
set PATH=%PATH%;C:\dev\flutter\bin

echo [4/8] Downloading Android Command Line Tools...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Write-Host 'Downloading Android tools...'; Invoke-WebRequest -Uri 'https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip' -OutFile 'android-tools.zip' -UseBasicParsing; Write-Host 'Download complete'}"

echo [5/8] Setting up Android SDK...
if not exist "C:\dev\android-sdk" mkdir "C:\dev\android-sdk"
powershell -Command "Write-Host 'Extracting Android tools...'; Expand-Archive -Path 'android-tools.zip' -DestinationPath 'C:\dev\android-sdk' -Force; Write-Host 'Extraction complete'"

echo [6/8] Setting Android environment variables...
setx ANDROID_HOME "C:\dev\android-sdk" /M
setx PATH "%PATH%;C:\dev\android-sdk\cmdline-tools\latest\bin;C:\dev\android-sdk\platform-tools" /M

echo [7/8] Installing Android SDK components...
cd /d "C:\dev\android-sdk\cmdline-tools\latest\bin"
echo y | sdkmanager.bat --sdk_root=C:\dev\android-sdk "platform-tools" "platforms;android-34" "build-tools;34.0.0" "cmdline-tools;latest"

echo [8/8] Accepting Android licenses...
echo y | sdkmanager.bat --sdk_root=C:\dev\android-sdk --licenses

echo.
echo ========================================
echo    INSTALLATION COMPLETE!
echo ========================================
echo.
echo Flutter SDK: C:\dev\flutter
echo Android SDK: C:\dev\android-sdk
echo.
echo NEXT STEPS:
echo 1. Close this window
echo 2. Open NEW command prompt (to get updated PATH)
echo 3. Connect your phone via USB
echo 4. Enable USB debugging on phone
echo 5. Run: flutter doctor
echo 6. Run: adb devices
echo.
pause
