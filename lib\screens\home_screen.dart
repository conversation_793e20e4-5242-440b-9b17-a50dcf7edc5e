import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/card_provider.dart';
import '../models/identification_card.dart';
import 'card_generation_screen.dart';
import 'qr_scanner_screen.dart';
import 'card_list_screen.dart';
import '../widgets/card_preview_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CardProvider>().loadCards();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CryptoID'),
        centerTitle: true,
      ),
      body: Consumer<CardProvider>(
        builder: (context, cardProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Welcome to CryptoID',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Generate secure identification cards and verify them with cryptographic technology.',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: _ActionCard(
                        title: 'Generate Card',
                        subtitle: 'Create new ID cards',
                        icon: Icons.add_card,
                        color: Colors.blue,
                        onTap: () => _navigateToCardGeneration(context),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _ActionCard(
                        title: 'Scan QR Code',
                        subtitle: 'Verify card authenticity',
                        icon: Icons.qr_code_scanner,
                        color: Colors.green,
                        onTap: () => _navigateToScanner(context),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: _ActionCard(
                        title: 'View All Cards',
                        subtitle: 'Manage your cards',
                        icon: Icons.credit_card,
                        color: Colors.orange,
                        onTap: () => _navigateToCardList(context),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Statistics Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Statistics',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _StatItem(
                              label: 'Total Cards',
                              value: cardProvider.cards.length.toString(),
                              color: Colors.blue,
                            ),
                            _StatItem(
                              label: 'Professional',
                              value: cardProvider.getCardsByType(CardType.professional).length.toString(),
                              color: Colors.green,
                            ),
                            _StatItem(
                              label: 'School',
                              value: cardProvider.getCardsByType(CardType.school).length.toString(),
                              color: Colors.orange,
                            ),
                            _StatItem(
                              label: 'Business',
                              value: cardProvider.getCardsByType(CardType.business).length.toString(),
                              color: Colors.purple,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Recent Cards
                if (cardProvider.cards.isNotEmpty) ...[
                  const Text(
                    'Recent Cards',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...cardProvider.cards.take(3).map((card) => 
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: CardPreviewWidget(card: card),
                    ),
                  ),
                ],
                
                if (cardProvider.isLoading)
                  const Center(child: CircularProgressIndicator()),
                
                if (cardProvider.error != null)
                  Card(
                    color: Colors.red.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        cardProvider.error!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _navigateToCardGeneration(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CardGenerationScreen()),
    );
  }

  void _navigateToScanner(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const QRScannerScreen()),
    );
  }

  void _navigateToCardList(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CardListScreen()),
    );
  }
}

class _ActionCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _ActionCard({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, size: 48, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StatItem extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _StatItem({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}
