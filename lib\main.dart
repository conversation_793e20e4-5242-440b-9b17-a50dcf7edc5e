import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'services/database_service.dart';
import 'services/crypto_service.dart';
import 'providers/card_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database (skip for web)
  try {
    await DatabaseService.instance.database;
  } catch (e) {
    print('Database initialization skipped for web: $e');
  }

  runApp(const CryptoIDApp());
}

class CryptoIDApp extends StatelessWidget {
  const CryptoIDApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CardProvider()),
        Provider(create: (_) => CryptoService()),
      ],
      child: MaterialApp(
        title: 'CryptoID',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            elevation: 2,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        home: const HomeScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
