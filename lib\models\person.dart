class Person {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String? photoPath;
  final DateTime dateOfBirth;
  final String address;
  final DateTime createdAt;

  Person({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    this.photoPath,
    required this.dateOfBirth,
    required this.address,
    required this.createdAt,
  });

  String get fullName => '$firstName $lastName';

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'photoPath': photoPath,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'address': address,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Person.fromJson(Map<String, dynamic> json) {
    return Person(
      id: json['id'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      email: json['email'],
      phone: json['phone'],
      photoPath: json['photoPath'],
      dateOfBirth: DateTime.parse(json['dateOfBirth']),
      address: json['address'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'photo_path': photoPath,
      'date_of_birth': dateOfBirth.toIso8601String(),
      'address': address,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory Person.fromMap(Map<String, dynamic> map) {
    return Person(
      id: map['id'],
      firstName: map['first_name'],
      lastName: map['last_name'],
      email: map['email'],
      phone: map['phone'],
      photoPath: map['photo_path'],
      dateOfBirth: DateTime.parse(map['date_of_birth']),
      address: map['address'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }
}
