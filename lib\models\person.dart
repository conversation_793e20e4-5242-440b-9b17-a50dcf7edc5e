class Person {
  final String? id;
  final String name;
  final String email;
  final String phone;
  final String idNumber;
  final String idType;
  final String? department;
  final String? position;
  final String? company;
  final String? address;
  final String? emergencyContact;
  final String? photoPath;
  final DateTime createdAt;

  Person({
    this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.idNumber,
    required this.idType,
    this.department,
    this.position,
    this.company,
    this.address,
    this.emergencyContact,
    this.photoPath,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'idNumber': idNumber,
      'idType': idType,
      'department': department,
      'position': position,
      'company': company,
      'address': address,
      'emergencyContact': emergencyContact,
      'photoPath': photoPath,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Person.fromJson(Map<String, dynamic> json) {
    return Person(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      idNumber: json['idNumber'],
      idType: json['idType'],
      department: json['department'],
      position: json['position'],
      company: json['company'],
      address: json['address'],
      emergencyContact: json['emergencyContact'],
      photoPath: json['photoPath'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'id_number': idNumber,
      'id_type': idType,
      'department': department,
      'position': position,
      'company': company,
      'address': address,
      'emergency_contact': emergencyContact,
      'photo_path': photoPath,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory Person.fromMap(Map<String, dynamic> map) {
    return Person(
      id: map['id'],
      name: map['name'],
      email: map['email'],
      phone: map['phone'],
      idNumber: map['id_number'],
      idType: map['id_type'],
      department: map['department'],
      position: map['position'],
      company: map['company'],
      address: map['address'],
      emergencyContact: map['emergency_contact'],
      photoPath: map['photo_path'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }
}
