import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/person.dart';
import '../models/identification_card.dart';
import '../providers/card_provider.dart';
import 'card_preview_screen.dart';

class CardGenerationScreen extends StatefulWidget {
  const CardGenerationScreen({super.key});

  @override
  State<CardGenerationScreen> createState() => _CardGenerationScreenState();
}

class _CardGenerationScreenState extends State<CardGenerationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _uuid = const Uuid();
  
  // Form controllers
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _organizationController = TextEditingController();
  final _positionController = TextEditingController();
  final _departmentController = TextEditingController();
  final _employeeIdController = TextEditingController();
  
  CardType _selectedCardType = CardType.professional;
  DateTime _dateOfBirth = DateTime.now().subtract(const Duration(days: 365 * 25));
  DateTime _expiryDate = DateTime.now().add(const Duration(days: 365 * 2));
  
  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _organizationController.dispose();
    _positionController.dispose();
    _departmentController.dispose();
    _employeeIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generate ID Card'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Card Type Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Card Type',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _CardTypeOption(
                              cardType: CardType.professional,
                              selectedType: _selectedCardType,
                              onChanged: (type) => setState(() => _selectedCardType = type),
                              title: 'Professional',
                              subtitle: 'Work badges',
                              icon: Icons.work,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _CardTypeOption(
                              cardType: CardType.school,
                              selectedType: _selectedCardType,
                              onChanged: (type) => setState(() => _selectedCardType = type),
                              title: 'School',
                              subtitle: 'Student IDs',
                              icon: Icons.school,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _CardTypeOption(
                              cardType: CardType.business,
                              selectedType: _selectedCardType,
                              onChanged: (type) => setState(() => _selectedCardType = type),
                              title: 'Business',
                              subtitle: 'Business cards',
                              icon: Icons.business,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Personal Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Personal Information',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _firstNameController,
                              decoration: const InputDecoration(
                                labelText: 'First Name',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) => value?.isEmpty == true ? 'Required' : null,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _lastNameController,
                              decoration: const InputDecoration(
                                labelText: 'Last Name',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) => value?.isEmpty == true ? 'Required' : null,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value?.isEmpty == true) return 'Required';
                          if (!value!.contains('@')) return 'Invalid email';
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'Phone',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Required' : null,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _addressController,
                        decoration: const InputDecoration(
                          labelText: 'Address',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                        validator: (value) => value?.isEmpty == true ? 'Required' : null,
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        title: const Text('Date of Birth'),
                        subtitle: Text('${_dateOfBirth.day}/${_dateOfBirth.month}/${_dateOfBirth.year}'),
                        trailing: const Icon(Icons.calendar_today),
                        onTap: _selectDateOfBirth,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Organization Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Organization Information',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _organizationController,
                        decoration: InputDecoration(
                          labelText: _getOrganizationLabel(),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Required' : null,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _positionController,
                        decoration: InputDecoration(
                          labelText: _getPositionLabel(),
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Required' : null,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _departmentController,
                        decoration: const InputDecoration(
                          labelText: 'Department (Optional)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _employeeIdController,
                        decoration: InputDecoration(
                          labelText: _getIdLabel(),
                          border: const OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        title: const Text('Expiry Date'),
                        subtitle: Text('${_expiryDate.day}/${_expiryDate.month}/${_expiryDate.year}'),
                        trailing: const Icon(Icons.calendar_today),
                        onTap: _selectExpiryDate,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Generate Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _generateCard,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Generate Card',
                    style: TextStyle(fontSize: 18),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getOrganizationLabel() {
    switch (_selectedCardType) {
      case CardType.professional:
        return 'Company Name';
      case CardType.school:
        return 'School Name';
      case CardType.business:
        return 'Business Name';
    }
  }

  String _getPositionLabel() {
    switch (_selectedCardType) {
      case CardType.professional:
        return 'Job Title';
      case CardType.school:
        return 'Grade/Class';
      case CardType.business:
        return 'Position';
    }
  }

  String _getIdLabel() {
    switch (_selectedCardType) {
      case CardType.professional:
        return 'Employee ID';
      case CardType.school:
        return 'Student ID';
      case CardType.business:
        return 'Business ID';
    }
  }

  Future<void> _selectDateOfBirth() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dateOfBirth,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() => _dateOfBirth = date);
    }
  }

  Future<void> _selectExpiryDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _expiryDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    if (date != null) {
      setState(() => _expiryDate = date);
    }
  }

  Future<void> _generateCard() async {
    if (!_formKey.currentState!.validate()) return;

    final person = Person(
      id: _uuid.v4(),
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      email: _emailController.text.trim(),
      phone: _phoneController.text.trim(),
      dateOfBirth: _dateOfBirth,
      address: _addressController.text.trim(),
      createdAt: DateTime.now(),
    );

    final cardProvider = context.read<CardProvider>();
    
    final card = await cardProvider.createCard(
      person: person,
      cardType: _selectedCardType,
      organizationName: _organizationController.text.trim(),
      position: _positionController.text.trim(),
      department: _departmentController.text.trim().isEmpty ? null : _departmentController.text.trim(),
      studentId: _selectedCardType == CardType.school ? _employeeIdController.text.trim() : null,
      employeeId: _selectedCardType != CardType.school ? _employeeIdController.text.trim() : null,
      expiryDate: _expiryDate,
    );

    if (card != null) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => CardPreviewScreen(card: card),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(cardProvider.error ?? 'Failed to generate card'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

class _CardTypeOption extends StatelessWidget {
  final CardType cardType;
  final CardType selectedType;
  final Function(CardType) onChanged;
  final String title;
  final String subtitle;
  final IconData icon;

  const _CardTypeOption({
    required this.cardType,
    required this.selectedType,
    required this.onChanged,
    required this.title,
    required this.subtitle,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = cardType == selectedType;
    
    return GestureDetector(
      onTap: () => onChanged(cardType),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Colors.blue.shade50 : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: isSelected ? Colors.blue : Colors.grey,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.blue : Colors.black,
              ),
            ),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
