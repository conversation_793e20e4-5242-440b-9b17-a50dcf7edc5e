# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 680ms
    create-module-model completed in 689ms
    create-variant-model 10ms
    create-module-model
      create-cmake-model 762ms
    create-module-model completed in 771ms
    [gap of 28ms]
    create-X86_64-model 11ms
    create-module-model
      create-cmake-model 598ms
    create-module-model completed in 606ms
    [gap of 28ms]
  create-initial-cxx-model completed in 2172ms
  [gap of 32ms]
create_cxx_tasks completed in 2204ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 15ms]
      create-cmake-model 554ms
    create-module-model completed in 574ms
    create-variant-model 10ms
    [gap of 20ms]
    create-X86_64-model 10ms
    create-module-model
      create-cmake-model 560ms
    create-module-model completed in 571ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model
      [gap of 13ms]
      create-cmake-model 491ms
    create-module-model completed in 508ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 12ms
    [gap of 10ms]
    create-X86-model 10ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 1819ms
  [gap of 79ms]
create_cxx_tasks completed in 1900ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 11ms]
      create-cmake-model 828ms
    create-module-model completed in 845ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model
      [gap of 14ms]
      create-cmake-model 712ms
    create-module-model completed in 731ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 20ms
    create-module-model
      create-cmake-model 644ms
    create-module-model completed in 655ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 14ms
    [gap of 10ms]
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 2441ms
  [gap of 17ms]
create_cxx_tasks completed in 2458ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 10ms]
      create-cmake-model 851ms
    create-module-model completed in 865ms
    create-module-model
      create-cmake-model 772ms
    create-module-model completed in 784ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 10ms
    create-module-model
      [gap of 10ms]
      create-cmake-model 701ms
      [gap of 12ms]
    create-module-model completed in 723ms
    [gap of 12ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 2523ms
create_cxx_tasks completed in 2531ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 19ms]
      create-ndk-meta-abi-list 36ms
      create-cmake-model 2062ms
    create-module-model completed in 2125ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 11ms
    create-module-model
      create-cmake-model 2172ms
    create-module-model completed in 2184ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 13ms
    create-X86-model 22ms
    create-X86_64-model 13ms
    create-module-model
      [gap of 13ms]
      create-cmake-model 2347ms
    create-module-model completed in 2365ms
    create-variant-model 16ms
    create-ARM64_V8A-model 31ms
    create-X86-model 32ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 6940ms
  [gap of 11ms]
create_cxx_tasks completed in 6951ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 16ms]
      create-cmake-model 1756ms
    create-module-model completed in 1779ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 21ms
    create-X86-model 30ms
    create-X86_64-model 18ms
    create-module-model
      [gap of 12ms]
      create-cmake-model 1725ms
    create-module-model completed in 1743ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    [gap of 10ms]
    create-X86_64-model 13ms
    create-module-model
      [gap of 20ms]
      create-cmake-model 1728ms
    create-module-model completed in 1753ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 80ms
    create-X86_64-model 36ms
  create-initial-cxx-model completed in 5645ms
  [gap of 134ms]
create_cxx_tasks completed in 5781ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 23ms]
      create-cmake-model 3083ms
    create-module-model completed in 3111ms
    create-variant-model 11ms
    [gap of 10ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    create-module-model
      create-cmake-model 2577ms
    create-module-model completed in 2588ms
    create-variant-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model
      [gap of 27ms]
      create-cmake-model 2012ms
    create-module-model completed in 2043ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 7902ms
  [gap of 59ms]
create_cxx_tasks completed in 7971ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 626ms
    create-module-model completed in 632ms
    create-module-model
      create-cmake-model 490ms
    create-module-model completed in 495ms
    create-module-model
      create-cmake-model 664ms
    create-module-model completed in 670ms
    [gap of 23ms]
  create-initial-cxx-model completed in 1870ms
create_cxx_tasks completed in 1873ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 871ms
    create-module-model completed in 881ms
    create-module-model
      create-cmake-model 808ms
    create-module-model completed in 815ms
    create-module-model
      create-cmake-model 811ms
    create-module-model completed in 816ms
    [gap of 28ms]
  create-initial-cxx-model completed in 2603ms
create_cxx_tasks completed in 2607ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 1286ms
    create-module-model completed in 1298ms
    create-variant-model 10ms
    create-module-model
      create-cmake-model 1533ms
    create-module-model completed in 1543ms
    create-ARMEABI_V7A-model 10ms
    create-module-model
      create-cmake-model 1534ms
    create-module-model completed in 1543ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 25ms]
  create-initial-cxx-model completed in 4516ms
create_cxx_tasks completed in 4521ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 503ms
    create-module-model completed in 513ms
    create-module-model
      create-cmake-model 676ms
    create-module-model completed in 682ms
    create-module-model
      create-cmake-model 739ms
    create-module-model completed in 748ms
    [gap of 24ms]
  create-initial-cxx-model completed in 2022ms
  [gap of 26ms]
create_cxx_tasks completed in 2048ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 489ms
    create-module-model completed in 495ms
    create-module-model
      create-cmake-model 585ms
    create-module-model completed in 590ms
    create-module-model
      create-cmake-model 691ms
      [gap of 12ms]
    create-module-model completed in 706ms
    [gap of 17ms]
  create-initial-cxx-model completed in 1844ms
create_cxx_tasks completed in 1846ms

# C/C++ build system timings# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 599ms
    create-module-model completed in 607ms
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 689ms
    create-module-model completed in 698ms
    create-module-model
      create-cmake-model 703ms
    create-module-model completed in 710ms
    create-module-model
      create-cmake-model 661ms
    create-module-model completed in 668ms
    create-module-model
      create-cmake-model 643ms
    create-module-model completed in 649ms
    [gap of 19ms]
  create-initial-cxx-model completed in 2035ms
create_cxx_tasks completed in 2039ms


    create-module-model
      create-cmake-model 934ms
    create-module-model completed in 942ms
    [gap of 37ms]
  create-initial-cxx-model completed in 2407ms
create_cxx_tasks completed in 2414ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 582ms
    create-module-model completed in 587ms
    create-module-model
      create-cmake-model 833ms
    create-module-model completed in 836ms
    create-module-model
      create-cmake-model 588ms
    create-module-model completed in 594ms
    [gap of 19ms]
  create-initial-cxx-model completed in 2077ms
create_cxx_tasks completed in 2081ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 736ms
    create-module-model completed in 742ms
    create-module-model
      create-cmake-model 721ms
    create-module-model completed in 727ms
    create-module-model
      create-cmake-model 677ms
    create-module-model completed in 680ms
    [gap of 16ms]
  create-initial-cxx-model completed in 2204ms
create_cxx_tasks completed in 2206ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 830ms
    create-module-model completed in 842ms
    create-module-model
      create-cmake-model 770ms
    create-module-model completed in 779ms
    create-module-model
      create-cmake-model 609ms
    create-module-model completed in 615ms
    [gap of 15ms]
  create-initial-cxx-model completed in 2304ms
create_cxx_tasks completed in 2306ms

