# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 680ms
    create-module-model completed in 689ms
    create-variant-model 10ms
    create-module-model
      create-cmake-model 762ms
    create-module-model completed in 771ms
    [gap of 28ms]
    create-X86_64-model 11ms
    create-module-model
      create-cmake-model 598ms
    create-module-model completed in 606ms
    [gap of 28ms]
  create-initial-cxx-model completed in 2172ms
  [gap of 32ms]
create_cxx_tasks completed in 2204ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 15ms]
      create-cmake-model 554ms
    create-module-model completed in 574ms
    create-variant-model 10ms
    [gap of 20ms]
    create-X86_64-model 10ms
    create-module-model
      create-cmake-model 560ms
    create-module-model completed in 571ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model
      [gap of 13ms]
      create-cmake-model 491ms
    create-module-model completed in 508ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 12ms
    [gap of 10ms]
    create-X86-model 10ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 1819ms
  [gap of 79ms]
create_cxx_tasks completed in 1900ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 11ms]
      create-cmake-model 828ms
    create-module-model completed in 845ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model
      [gap of 14ms]
      create-cmake-model 712ms
    create-module-model completed in 731ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 20ms
    create-module-model
      create-cmake-model 644ms
    create-module-model completed in 655ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 14ms
    [gap of 10ms]
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 2441ms
  [gap of 17ms]
create_cxx_tasks completed in 2458ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 10ms]
      create-cmake-model 851ms
    create-module-model completed in 865ms
    create-module-model
      create-cmake-model 772ms
    create-module-model completed in 784ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 10ms
    create-module-model
      [gap of 10ms]
      create-cmake-model 701ms
      [gap of 12ms]
    create-module-model completed in 723ms
    [gap of 12ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 2523ms
create_cxx_tasks completed in 2531ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 19ms]
      create-ndk-meta-abi-list 36ms
      create-cmake-model 2062ms
    create-module-model completed in 2125ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 11ms
    create-module-model
      create-cmake-model 2172ms
    create-module-model completed in 2184ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 13ms
    create-X86-model 22ms
    create-X86_64-model 13ms
    create-module-model
      [gap of 13ms]
      create-cmake-model 2347ms
    create-module-model completed in 2365ms
    create-variant-model 16ms
    create-ARM64_V8A-model 31ms
    create-X86-model 32ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 6940ms
  [gap of 11ms]
create_cxx_tasks completed in 6951ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 16ms]
      create-cmake-model 1756ms
    create-module-model completed in 1779ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 21ms
    create-X86-model 30ms
    create-X86_64-model 18ms
    create-module-model
      [gap of 12ms]
      create-cmake-model 1725ms
    create-module-model completed in 1743ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 14ms
    [gap of 10ms]
    create-X86_64-model 13ms
    create-module-model
      [gap of 20ms]
      create-cmake-model 1728ms
    create-module-model completed in 1753ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 80ms
    create-X86_64-model 36ms
  create-initial-cxx-model completed in 5645ms
  [gap of 134ms]
create_cxx_tasks completed in 5781ms

