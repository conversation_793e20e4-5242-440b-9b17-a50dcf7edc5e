@echo off
echo ========================================
echo    QUICK FLUTTER SETUP FOR PHONE DEBUG
echo ========================================
echo.

:: Set local environment variables for this session
set FLUTTER_ROOT=%CD%\flutter
set ANDROID_HOME=%CD%\android-sdk
set PATH=%FLUTTER_ROOT%\bin;%ANDROID_HOME%\platform-tools;%PATH%

echo [1/5] Creating Flutter directory structure...
if not exist "flutter" mkdir flutter
if not exist "flutter\bin" mkdir flutter\bin
if not exist "android-sdk" mkdir android-sdk
if not exist "android-sdk\platform-tools" mkdir android-sdk\platform-tools

echo [2/5] Creating Flutter wrapper script...
echo @echo off > flutter\bin\flutter.bat
echo echo Flutter SDK not fully installed >> flutter\bin\flutter.bat
echo echo Please install Flutter manually from: https://flutter.dev >> flutter\bin\flutter.bat
echo echo. >> flutter\bin\flutter.bat
echo echo For now, let's try to run your app with available tools... >> flutter\bin\flutter.bat
echo pause >> flutter\bin\flutter.bat

echo [3/5] Creating ADB wrapper...
echo @echo off > android-sdk\platform-tools\adb.exe
echo echo ADB not installed >> android-sdk\platform-tools\adb.exe
echo echo Please install Android SDK >> android-sdk\platform-tools\adb.exe

echo [4/5] Checking if phone is connected via USB...
echo Looking for connected devices...
powershell -Command "Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like '*Android*' -or $_.Name -like '*ADB*' -or $_.Name -like '*Phone*'} | Select-Object Name, Status"

echo [5/5] Setting up project for manual Flutter installation...
echo.
echo ========================================
echo    SETUP INSTRUCTIONS
echo ========================================
echo.
echo Your CryptoID Flutter project is ready!
echo.
echo TO COMPLETE SETUP:
echo 1. Download Flutter from: https://docs.flutter.dev/get-started/install/windows
echo 2. Extract to: %CD%\flutter
echo 3. Download Android Studio from: https://developer.android.com/studio
echo 4. Install Android SDK
echo 5. Connect your phone with USB debugging enabled
echo.
echo THEN RUN:
echo   flutter doctor
echo   flutter pub get
echo   flutter run
echo.
echo Your phone should be connected and have:
echo - Developer options enabled
echo - USB debugging enabled
echo - Allow installation from unknown sources
echo.
pause
